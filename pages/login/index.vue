<template>
	<view class="login">
		<!-- 状态栏 -->
		<view class="status-bar"></view>
		
		<!-- 登录已成功的界面 -->
		<template v-if="isLogin">
			<!-- 背景 -->
			<view class="login-background">
				<image class="bg-image" src="/static/figma-assets/background-image.png" mode="aspectFill"></image>
				<view class="bg-overlay"></view>
			</view>
			
			<!-- 主要内容 -->
			<view class="login-main">
				<!-- 标题区域 -->
				<view class="title-section">
					<view class="system-title">心理评估系统</view>
					<view class="hospital-name">{{title||'浙江大学医学院附属儿童医院'}}</view>
				</view>
				
				<!-- 功能卡片区域 -->
				<view class="cards-section">
					<view class="card-item" @click="()=>go(0)">
						<view class="card-background"></view>
						<view class="card-shadow"></view>
						<image class="card-icon" src="/static/figma-assets/patient-management-icon.png" mode="aspectFit"></image>
						<view class="card-indicator"></view>
						<view class="card-title">患者管理</view>
					</view>
					
					<view class="card-item" @click="()=>goWorkOrder()">
						<view class="card-background"></view>
						<view class="card-shadow"></view>
						<image class="card-icon" src="/static/figma-assets/work-order-icon.png" mode="aspectFit"></image>
						<view class="card-indicator"></view>
						<view class="card-title">测评工单</view>
					</view>
					
					<view class="card-item" @click="()=>go(1)">
						<view class="card-background"></view>
						<view class="card-shadow"></view>
						<image class="card-icon" src="/static/figma-assets/record-icon.png" mode="aspectFit"></image>
						<view class="card-indicator"></view>
						<view class="card-title">测评记录</view>
					</view>
				</view>
				
				<!-- 底部按钮区域 -->
				<view class="bottom-buttons">
					<view class="bottom-btn device-btn" @click="()=>go(2)">
						<image class="btn-icon" src="/static/figma-assets/device-icon.png" mode="aspectFit"></image>
						<text class="btn-text">设备链接</text>
					</view>
					<view class="bottom-btn logout-btn" @click="outLogin">
						<image class="btn-icon" src="/static/figma-assets/logout-icon.png" mode="aspectFit"></image>
						<text class="btn-text">退出登录</text>
					</view>
				</view>
			</view>
		</template>
		
		<!-- 登录界面 -->
		<template v-else>
			<view class="login-container">
				<!-- 左侧插图区域 -->
				<view class="login-left">
					<image class="login-bg-image" src="/static/login/login-bg-left-596665.png" mode="aspectFill"></image>
					<image class="login-illustration" src="/static/login/login-bg-right-4522d6.png" mode="aspectFill"></image>
				</view>
				
				<!-- 右侧登录表单区域 -->
				<view class="login-right">
					<view class="login-form">
						<view class="form-field">
							<view class="field-label-wrapper">
								<image class="field-icon" src="/static/login/login-phone-icon.png" mode="aspectFit"></image>
								<view class="field-label">手机号码</view>
							</view>
							<view class="field-input-wrapper">
								<input 
									class="field-input" 
									v-model="state.userInfo.account" 
									type="number" 
									placeholder="请输入您的手机号码"
									placeholder-style="color: #EEEEEE; font-size: 56rpx;"
								/>
							</view>
							<view class="field-line"></view>
						</view>
						
						<view class="form-field">
							<view class="field-label-wrapper">
								<image class="field-icon" src="/static/login/login-lock-icon.png" mode="aspectFit"></image>
								<view class="field-label">密码</view>
							</view>
							<view class="field-input-wrapper">
								<input 
									class="field-input" 
									v-model="state.userInfo.password" 
									:password="!showPassword"
									placeholder="请输入您的密码"
									placeholder-style="color: #EEEEEE; font-size: 56rpx;"
								/>
								<image class="eye-icon" src="/static/login/login-eye-closed.png" v-if="!showPassword" @click="togglePassword" mode="aspectFit"></image>
								<image class="eye-icon" src="/static/login/login-eye-open.png" v-if="showPassword" @click="togglePassword" mode="aspectFit"></image>
							</view>
							<view class="field-line"></view>
						</view>
						
						<view class="login-btn" :class="{ active: isFormValid }" @click="submit">
							<text class="login-btn-text">登录</text>
						</view>
					</view>
					
					<!-- 隐私协议 -->
					<view class="privacy-section">
						<view class="privacy-content">
							<view class="privacy-checkbox" :class="{ checked: agreePrivacy }" @click="togglePrivacy"></view>
							<text class="privacy-text">未注册手机号登录后将自动注册生成账号，勾选表示您已阅读并同意《用户服务协议》、《隐私政策》</text>
						</view>
					</view>
				</view>
			</view>
		</template>
		
		<uv-popup ref="popup" bgColor="none">
			<view class="login-pop">
				<view class="login-pop-close iconfont" @click="popup.close()">&#xe66a;</view>
				<view>请扫码联系脑域奇迹工作人员</view>
				<image class="login-pop-img" src="/static/qr.png" mode="widthFix"></image>
			</view>
		</uv-popup>
	</view>
</template>

<script setup>
	import {
		reactive,
		ref,
		onMounted,
		computed
	} from 'vue';
	import {
		showToast,
		redirectTo,
		setStorage,
		getStorageSync,
		navigateTo,
		clearStorage
	} from '../../common/uniTool.js'
	import {
		onShow,
		onHide
	} from '@dcloudio/uni-app'
	import {
		getUserInfo,
		login
	} from '../../service/index';
	import {
		useHelper
	} from "../../stores/helper";
	import {
		getTime
	} from '../../common/method.js';
	import {
		useUserStore
	} from '../../stores/user.js';
	const form = ref(null)
	const helper = useHelper(); //设备仓库
	const isLogin = ref(false) //是否登录
	const popup = ref() //账号限制
	const userStore = useUserStore()
	const showPassword = ref(false) //是否显示密码
	const agreePrivacy = ref(false) //是否同意隐私协议（默认未勾选）
	// const dateText = ref(0) //时间
	const title = ref('')
	const customTextStyle = "font-size: 38rpx;font-weight: bold;"
	const bottomStyle = 'margin-top: 20px;background: linear-gradient( 270deg, #1DA2FF 0%, #2073FF 100%);'
	const customStyle = 'background: #F5F6FA;width: 256rpx;height: 68rpx;font-weight: 400;font-size: 15rpx;color: #111111;'
	const list = [{
			name: '患者管理',
			icon: '\ue66c',
			background: "linear-gradient( 135deg, #308EFC 0%, #35B5FC 100%)",
			boxShadow: "0rpx 6rpx 20rpx 0rpx #D8E4F5, inset 0rpx 4 8rpx 0rpx #4FB6FE",
		},
		{
			name: '测评记录',
			icon: '\ue66b',
			background: "linear-gradient( 135deg, #4A75F9 0%, #7394FE 100%)",
			boxShadow: "0rpx 6rpx 20rpx 0rpx #D8DDF5, inset 0rpx 4 8rpx 0rpx #7899FF",
		},
		{
			name: '设备连接',
			icon: '\ue66e',
			background: "linear-gradient( 135deg, #08C29B 0%, #21D8A8 100%)",
			boxShadow: "0rpx 6rpx 20rpx 0rpx #D8F3EB, inset 0rpx 4 8rpx 0rpx #29DCB4",
		}
	]
	const state = reactive({
		userInfo: {
			account: '',
			password: '',
		},
		rules: {
			'account': {
				type: 'number',
				required: true,
				message: '请填写账号',
				trigger: ['blur', 'change']
			},
			'password': {
				type: 'string',
				required: true,
				message: '请填写密码',
				trigger: ['blur', 'change']
			},
		}
	})
	
	// 切换密码显示状态
	const togglePassword = () => {
		showPassword.value = !showPassword.value
	}
	
	// 切换隐私协议同意状态
	const togglePrivacy = () => {
		agreePrivacy.value = !agreePrivacy.value
	}
	
	// 计算表单是否有效
	const isFormValid = computed(() => {
		return state.userInfo.account && state.userInfo.password
	})
	
	onShow(() => {
		//#ifdef APP-PLUS
		// plus.navigator.setFullscreen(true); // 注释掉全屏模式，显示状态栏
		// 设置状态栏文字为黑色
		plus.navigator.setStatusBarStyle('dark');
		// #endif
		// getUser()
		let token = getStorageSync('token')
		if (token) {
			getUser()
		}
	})
	onMounted(() => {
		console.log('初始化');
		//#ifdef APP-PLUS
		if (!helper.helperBlu) {
			helper.helperBlu = uni.requireNativePlugin('alghelper')
			console.log(1);
		}
		//#endif
		
		// 添加延时来确保DOM渲染完成后获取尺寸
		setTimeout(() => {
			getContainerSize();
		}, 500);
	})
	
	// 获取登录界面容器尺寸的方法
	const getContainerSize = () => {
		const query = uni.createSelectorQuery();
		
		// 获取登录容器尺寸（未登录状态）
		query.select('.login-container').boundingClientRect((rect) => {
			if (rect) {
				// 尺寸信息日志已删除
			}
		});
		
		// 获取登录后主要内容容器尺寸（已登录状态）
		// query.select('.login-main').boundingClientRect((rect) => {
		// 	if (rect) {
		// 		console.log('=== 登录后主要内容尺寸信息 ===');
		// 		console.log('主要内容容器宽度:', rect.width + 'px');
		// 		console.log('主要内容容器高度:', rect.height + 'px');
		// 		console.log('主要内容容器位置 - left:', rect.left + 'px', 'top:', rect.top + 'px');
		// 	}
		// });
		
		// 获取整个登录页面容器尺寸
		query.select('.login').boundingClientRect((rect) => {
			if (rect) {
				// 尺寸信息日志已删除
			}
		});
		
		query.exec();
	}
	const outLogin = () => {
		uni.showModal({
			title: '',
			content: '确定退出登录吗',
			showCancel: true,
			cancelText: '否',
			confirmText: '是',
			success: res => {
				if (res.confirm) {
					clearStorage();
					isLogin.value = false
					// 退出登录后延时获取尺寸信息
					setTimeout(() => {
						getContainerSize();
					}, 200);
				}
			},
			fail: () => {},
			complete: () => {}
		});

	}
	const getUser = () => {
		getUserInfo().then(res => {
			let nowTime = Date.now();
			if (nowTime <= res.data.endDate) {
				title.value = res.data.title			
				userStore.userInfo = res.data
				// dateText.value = Math.floor((res.data.endDate - nowTime) / (1000 * 3600 * 24))
				isLogin.value = true
				// 登录成功后延时获取尺寸信息
				setTimeout(() => {
					getContainerSize();
				}, 200);
			} else {
				popup.value.open('center')
				isLogin.value = false
			}
		})
	}
	// onShow(() => {
	// 	// #ifdef APP-PLUS
	// 	uni.showLoading({
	// 		title: "正在进入列表..."
	// 	})
	// 	setTimeout(() => {
	// 		plus.screen.lockOrientation('landscape-primary');
	// 		uni.hideLoading();
	// 		state.show = true
	// 	}, 200)
	// 	// #endif
	// })
	const go = (index) => {
		if (!index) {
			redirectTo('/pages/record/index')
		} else if (index === 1) {
			navigateTo('/pages/report/search')
		} else {
			navigateTo('/pages/device/index')
		}
	}
	
	// 新增测评工单跳转方法
	const goWorkOrder = () => {
		navigateTo('/pages/assessment/index')
	}
	
	const submit = () => {
		// 检查表单验证
		if (!state.userInfo.account) {
			showToast('请填写账号')
			return
		}
		if (!state.userInfo.password) {
			showToast('请填写密码')
			return
		}
		if (!agreePrivacy.value) {
			showToast('请先勾选用户服务协议和隐私政策')
			return
		}
		
		login(state.userInfo).then(res => {
			let nowTime = Date.now();
			if (nowTime >= res.data.beginDate && nowTime <= res.data.endDate && res.data.useFlag === '1') {
				// dateText.value = Math.floor((res.data.endDate - nowTime) / (1000 * 3600 * 24))
				uni.setStorageSync('token', res.data.token)
				userStore.userInfo = res.data
				title.value = res.data.title
				isLogin.value = true
				// 登录成功后延时获取尺寸信息
				setTimeout(() => {
					getContainerSize();
				}, 200);
			} else {
				popup.value.open('center')
			}
		}).catch(err => {
			showToast('登录失败')
		})
	}
</script>

<style lang="scss">
	.login {
		width: 100vw;
		height: 100vh;
		background: #D8E8FF;
		overflow: hidden;
		display: flex;
		flex-direction: column;
		position: relative;

		// 状态栏
		.status-bar {
			width: 100%;
			height: 96rpx; // 48px * 2
			background: none;
			position: relative;
			z-index: 10;
		}

		// 登录后界面的背景
		.login-background {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			z-index: 1;

			.bg-image {
				position: absolute;
				/* 反转图片（水平 + 垂直） */
				top: -512rpx;
				left: -2978rpx;
				transform: scaleX(-1) scaleY(-1);
				transform-origin: center;
				width: 7338rpx;
				height: 4128rpx;
				z-index: 1;
			}

			.bg-overlay {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: linear-gradient(180deg, rgba(255, 255, 255, 0) 0%, rgba(40, 127, 255, 1) 84.62%);
				opacity: 0.1;
				z-index: 2;
			}
		}

		// 登录后的主要内容
		.login-main {
			position: relative;
			z-index: 5;
			flex: 1;
			display: flex;
			flex-direction: column;
			padding: 0;
		}

		// 标题区域
		.title-section {
			text-align: center;
			margin-top: 80rpx; // 减少上边距
			margin-bottom: 60rpx; // 减少下边距

			.system-title {
				font-family: Alibaba PuHuiTi;
				font-weight: 400;
				font-size: 100rpx; // 修改为50px，与Figma一致
				color: #333333;
				line-height: 1em;
				margin-bottom: 80rpx;
			}

			.hospital-name {
				font-family: Alibaba PuHuiTi;
				font-weight: 400;
				font-size: 144rpx;
				color: #287FFF;
				line-height: 1em;
			}
		}

		// 功能卡片区域
		.cards-section {
			display: flex;
			justify-content: space-between;
			gap: 80rpx; // 修改为80rpx，卡片间距40px
			padding: 0 160rpx;
			margin-bottom: 60rpx; // 与底部按钮保持适当间距
		}

		.card-item {
			position: relative;
			flex: 1;
			aspect-ratio: 219/234; // 修改宽高比为219:234
			cursor: pointer;

			.card-background {
				position: absolute;
				top: 32rpx;
				left: 0;
				width: 100%;
				height: calc(100% - 32rpx);
				background: linear-gradient(135deg, #FFFFFF 0%, #E9F2FF 100%);
				border: 8rpx solid #FFFFFF;
				border-radius: 128rpx;
				z-index: 2;
			}

			.card-shadow {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: calc(100% - 32rpx);
				background: linear-gradient(135deg, #FFFFFF 0%, #E9F2FF 100%);
				border: 8rpx solid #FFFFFF;
				border-radius: 128rpx;
				z-index: 1;
			}

			.card-icon {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -60%);
				width: 40%;
				height: auto;
				aspect-ratio: 1;
				z-index: 3;
			}

			.card-indicator {
				position: absolute;
				bottom: 36%;
				left: 50%;
				transform: translateX(-50%);
				width: 200rpx;
				height: 32rpx;
				background: #E4EEFA;
				border-radius: 100rpx;
				filter: blur(32rpx);
				z-index: 3;
			}

			.card-title {
				position: absolute;
				bottom: 24%;
				left: 50%;
				transform: translateX(-50%);
				font-family: Alibaba PuHuiTi;
				font-weight: 400; // 修改为400，与Figma一致
				font-size: 56rpx; // 修改为28px，与Figma一致
				color: #333333;
				line-height: 1em;
				text-align: center;
				z-index: 3;
				white-space: nowrap;
			}
		}

		// 底部按钮区域
		.bottom-buttons {
			display: flex;
			justify-content: flex-end;
			gap: 64rpx;
			margin-bottom: 96rpx; // 距离屏幕底部48px
			margin-top: auto; // 自动撑开，让按钮位于底部
			padding: 0 160rpx; // 与卡片区域的padding保持一致，实现右侧对齐
		}

		.bottom-btn {
			display: flex;
			align-items: center;
			width: 336rpx; // 按照Figma设计稿168.14px ≈ 336rpx
			height: 139rpx; // 按照Figma设计稿69.48px ≈ 139rpx
			background: #7EB3FF;
			border-radius: 33rpx; // 按照Figma设计稿16.67px ≈ 33rpx
			padding: 0 33rpx; // 调整内边距以适应新尺寸
			cursor: pointer;

			.btn-icon {
				width: 56rpx;
				height: 56rpx;
				margin-right: 16rpx;
				// 确保图标为白色（通过filter实现）
				filter: brightness(0) invert(1);
			}

			.btn-text {
				font-family: Alibaba PuHuiTi;
				font-weight: 400;
				font-size: 44rpx; // 修改为22px，与Figma一致
				color: #FFFFFF;
				line-height: 1em;
			}
		}

		// 登录容器（左右分布）
		.login-container {
			display: flex;
			width: 78.1%; // 基于Figma设计稿的比例 (1042.19/1334)
			height: 74.2%; // 基于Figma设计稿的比例 (555.83/750)
			max-width: 1200px; // 最大宽度限制
			max-height: 641px; // 最大高度限制
			position: fixed; // 使用fixed定位确保相对于视窗居中
			top: 50%;
			left: 50%;
			transform: translate(-50%, -50%); // 完全居中
			box-shadow: 0rpx 16rpx 64rpx 0rpx rgba(51, 51, 51, 0.1);
			border-radius: 40rpx; // 恢复使用rpx单位，保持一致性
			overflow: hidden;
			z-index: 20; // 确保在状态栏之上
		}
		
		// 当屏幕宽度超过1534px时（1200px / 78.1% ≈ 1534px），使用固定尺寸
		@media (min-width: 1534px) {
			.login-container {
				width: 1200px;
				height: 641px;
			}
		}

		// 左侧插图区域
		.login-left {
			width: 40%; // 基于整体容器的40%比例
			height: 100%;
			background: #287FFF;
			border-radius: 40rpx 0 0 40rpx;
			position: relative;
			overflow: hidden;

			.login-bg-image {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 1;
				transform: scaleX(-1); // 左右镜像翻转
			}

			.login-illustration {
				position: absolute;
				top: -19%; // 使用百分比替代固定值
				right: -68%; // 使用百分比替代固定值
				width: 240%; // 使用百分比替代固定值
				height: 149%; // 使用百分比替代固定值
				z-index: 2;
				transform: scaleX(-1); // 添加左右镜像翻转
			}
		}

		// 右侧登录表单区域
		.login-right {
			flex: 1; // 占据剩余的60%空间
			background: #FFFFFF;
			border-radius: 0 40rpx 40rpx 0;
			padding: 100rpx 80rpx 0 80rpx; // 恢复使用rpx单位，调整合适的内边距
			position: relative;
			display: flex;
			flex-direction: column;
			align-items: center;
		}

		// 登录表单
		.login-form {
			width: 100%;
			max-width: none; // 移除固定最大宽度限制
		}

		// 表单字段
		.form-field {
			margin-bottom: 80rpx; // 恢复使用rpx单位

			.field-label-wrapper {
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				.field-icon {
					width: 56rpx; // 恢复使用rpx单位
					height: 56rpx;
					margin-right: 20rpx;
				}

				.field-label {
					font-family: Alibaba PuHuiTi;
					font-weight: 500;
					font-size: 56rpx; // 基于Figma设计稿调整字体大小
					color: #333333;
				}
			}

			.field-input-wrapper {
				position: relative;
				display: flex;
				align-items: center;
				margin-bottom: 20rpx;

				.field-input {
					flex: 1;
					font-family: Alibaba PuHuiTi;
					font-weight: 400;
					font-size: 40rpx; // 基于Figma设计稿调整字体大小
					color: #333333;
					background: transparent;
					border: none;
					outline: none;
					padding: 20rpx 0;
					min-height: 80rpx;
				}

				.eye-icon {
					width: 56rpx; // 恢复使用rpx单位
					height: 56rpx;
					cursor: pointer;
				}
			}

			.field-line {
				width: 100%;
				height: 2rpx; // 恢复使用rpx单位
				background: #D8D8D8;
			}
		}

		// 登录按钮
		.login-btn {
			width: 100%;
			height: 92rpx; // 基于Figma设计稿调整高度
			background: linear-gradient(90deg, #D4E7FF 0%, #C4DCFF 100%);
			border-radius: 22rpx; // 基于Figma设计稿调整圆角
			box-shadow: 0rpx 80rpx 80rpx 0rpx rgba(203, 227, 255, 0.24);
			display: flex;
			align-items: center;
			justify-content: center;
			margin-top: 120rpx; // 增加上边距，让按钮向下移动
			margin-bottom: 30rpx; // 设置与隐私协议的间距为30rpx
			cursor: pointer;
			transition: background 0.3s ease;

			&.active {
				background: linear-gradient(180deg, #217CFF 0%, #5EBAFF 100%);
			}

			.login-btn-text {
				font-family: Alibaba PuHuiTi;
				font-weight: 500;
				font-size: 56rpx; // 基于Figma设计稿调整字体大小
				color: #FFFFFF;
			}
		}

		// 隐私协议区域
		.privacy-section {
			position: absolute;
			bottom: 60rpx; // 调整底部间距，为登录按钮留出空间
			left: 80rpx;
			right: 80rpx;
			width: calc(100% - 160rpx); // 对应左右边距80rpx
			display: flex;
			justify-content: center;

			.privacy-content {
				width: 100%;
				max-width: none; // 移除固定最大宽度限制
				display: flex;
				align-items: flex-start;

				.privacy-checkbox {
					width: 56rpx; // 使用固定rpx值
					height: 56rpx;
					border: 4rpx solid #EEEEEE; // 使用固定rpx值
					border-radius: 50%;
					margin-right: 24rpx;
					flex-shrink: 0;
					cursor: pointer;
					position: relative;

					&.checked {
						background: #287FFF;
						border-color: #287FFF;

						&::after {
							content: '';
							position: absolute;
							left: 14rpx;
							top: 7rpx;
							width: 17rpx;
							height: 28rpx;
							border: 3rpx solid #FFFFFF;
							border-left: none;
							border-top: none;
							transform: rotate(45deg);
						}
					}
				}

				.privacy-text {
					font-family: Alibaba PuHuiTi;
					font-weight: 400;
					font-size: 34rpx; // 基于Figma设计稿调整字体大小
					color: #333333;
					line-height: 1.5;
					flex: 1;
				}
			}
		}

		// 弹窗样式
		&-pop {
			width: 735rpx;
			height: 750rpx;
			background: #FFFFFF;
			border-radius: 45rpx;
			overflow: hidden;
			display: flex;
			flex-direction: column;
			align-items: center;
			padding: 38rpx;
			font-family: PingFangSC, PingFang SC;
			font-weight: 400;
			font-size: 45rpx;
			color: #111111;

			&-close {
				align-self: flex-end;
				margin-bottom: 60rpx;
			}

			&-img {
				width: 374rpx;
				margin-top: 60rpx;
			}
		}
	}
</style>